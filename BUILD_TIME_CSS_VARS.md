# Build-Time CSS Variables Generation

This document explains the streamlined build-time CSS variable generation system for the Next UI design system.

## Overview

The system generates CSS variables from theme configurations at build time instead of runtime, providing better performance and avoiding layout shifts. CSS variables are generated for:

- **Colors**: All color palettes (slate, blue, red, etc.)
- **Tokens**: Theme-specific semantic tokens (primary, accent, text, background, etc.)
- **Design Tokens**: Typography, spacing, borders, shadows, etc.

## Architecture

### 1. Core CSS Generation (`packages/styles/src/utils/build-css.ts`)

Single utility for generating CSS from theme objects:

- `generateCompleteThemeCSS()`: Generates complete CSS with all themes, modes, and utilities

### 2. Build Script (`packages/styles/scripts/generate-css-vars.ts`)

Streamlined script that:
- Creates a combined `themes.css` file with all themes and modes
- Runs via `bun run build:css`

### 3. UnoCSS Integration (`packages/uno-preset/src/index.ts`)

Enhanced UnoCSS preset that:
- Includes CSS variables in the build output via `preflights`
- Supports multiple themes
- Configurable CSS variable inclusion

### 4. Vite Plugin (`packages/styles/src/vite-plugin.ts`)

Simple Vite plugin for build integration:
- Generates CSS during Vite build process
- Emits CSS assets automatically

## Generated CSS Structure

### CSS Variable Naming Convention

```css
--{category}-{path}: {value};
```

Examples:
```css
/* Colors */
--colors-blue-500: #3B82F6;
--colors-slate-100: #F1F5F9;

/* Semantic Tokens */
--tokens-primary-base: #2563EB;
--tokens-text-primary: #0F172A;
--tokens-background-surface: #F8FAFC;

/* Design Tokens */
--design-fontSize-lg: 1rem;
--design-spacing-md: 1.5rem;
--design-radii-lg: 0.5rem;
```

### Theme and Mode Selectors

```css
/* Default theme, light mode */
:root {
  --tokens-primary-base: #2563EB;
  --tokens-background-primary: #F1F5F9;
}

/* Dark mode */
[data-theme-mode="dark"] {
  --tokens-primary-base: #3B82F6;
  --tokens-background-primary: #1E293B;
}

/* Brand theme */
[data-theme="brand"] {
  --tokens-primary-base: #7C3AED;
}

/* Brand theme, dark mode */
[data-theme="brand"][data-theme-mode="dark"] {
  --tokens-primary-base: #8B5CF6;
}
```

## Usage

### 1. Build CSS Variables

```bash
# Generate CSS variables
bun run build:css

# Or from root
bun run build
```

### 2. Theme Provider Setup

The `NuiProvider` automatically applies theme and mode attributes:

```tsx
<NuiProvider themes={[neutralTheme, brandTheme]}>
  <App />
</NuiProvider>
```

This sets:
- `data-theme="neutral"` (or selected theme)
- `data-theme-mode="light"` (or selected mode)

### 3. Creating Themes

The `createTheme` utility automatically extends a base theme and supports two approaches:

#### Object Approach (Simple)
```tsx
// Minimal theme - just extends base theme
const minimalTheme = createTheme({
  name: "Minimal",
  // Everything else inherited automatically
});

// Custom colors
const customTheme = createTheme({
  name: "Custom",
  colors: {
    blue: {
      500: "#FF6B6B", // Override primary blue (base colors auto-merged)
    },
  },
});
```

#### Function Approach (With Base Theme Access)
```tsx
// Access base theme for referencing existing values
const advancedTheme = createTheme((baseTheme) => ({
  name: "Advanced",
  colors: {
    brand: {
      500: baseTheme.colors.blue[600], // Reference base colors
    },
  },
  modes: {
    vintage: {
      ...baseTheme.modes.light, // Extend existing mode
      primary: {
        base: baseTheme.colors.amber[600],
        hover: baseTheme.colors.amber[700],
      },
    },
  },
}));

// Modify existing modes
const modifiedTheme = createTheme((baseTheme) => ({
  name: "Modified",
  modes: {
    light: {
      ...baseTheme.modes.light,
      primary: {
        ...baseTheme.modes.light.primary,
        base: "#10B981", // Change just the base color
      },
    },
  },
}));
```

### 4. Using CSS Variables

```tsx
// Direct CSS variables
<div style={{
  backgroundColor: 'var(--tokens-primary-background)',
  color: 'var(--tokens-text-primary)',
  border: '1px solid var(--tokens-border-default)'
}}>
  Content
</div>

// With fallbacks
<div style={{
  backgroundColor: 'var(--tokens-primary-base, #2563EB)'
}}>
  Content
</div>
```

### 5. UnoCSS Integration

```typescript
// uno.config.ts
export default defineConfig({
  presets: [
    presetMini(),
    presetNextUI({
      themes: [neutralTheme, brandTheme],
      includeCSSVars: true, // Includes CSS variables in build
    })
  ],
});
```

## Generated Files

- `themes.css` - All themes and modes in a single file

## Benefits

1. **Performance**: No runtime CSS generation
2. **SSR Compatible**: CSS variables available immediately
3. **Type Safety**: Generated from TypeScript theme definitions
4. **Flexibility**: Support for multiple themes and modes
5. **Standard CSS**: Uses native CSS custom properties
6. **Build Optimization**: CSS can be minified and optimized
7. **Developer Experience**: Automatic generation with build process

## Build Integration

The system integrates with the build process:

1. **Development**: CSS variables generated via UnoCSS preset
2. **Production**: Pre-generated CSS files can be served statically
3. **CI/CD**: Build script runs automatically during build process

## Future Enhancements

- PostCSS plugin for additional processing
- CSS variable tree-shaking for unused variables
- Runtime theme switching with CSS variable updates
- Integration with CSS-in-JS libraries

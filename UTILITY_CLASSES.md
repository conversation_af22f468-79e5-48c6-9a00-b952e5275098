# Theme Utility Classes

The UnoCSS preset automatically generates utility classes for all theme tokens, allowing you to use semantic class names instead of CSS variables.

## 🎨 **Background Utilities**

### Primary Backgrounds
```html
<div class="bg-background-primary">Primary background</div>
<div class="bg-background-secondary">Secondary background</div>
<div class="bg-background-surface">Surface background</div>
<div class="bg-background-disabled">Disabled background</div>
```

### Semantic Backgrounds
```html
<div class="bg-primary-base">Primary color</div>
<div class="bg-primary-hover">Primary hover</div>
<div class="bg-primary-active">Primary active</div>
<div class="bg-primary-subtle">Primary subtle</div>
<div class="bg-primary-background">Primary background</div>

<div class="bg-accent-base">Accent color</div>
<div class="bg-positive-base">Success/positive</div>
<div class="bg-negative-base">Error/negative</div>
<div class="bg-info-base">Info color</div>
```

## 📝 **Text Color Utilities**

### Text Colors
```html
<span class="text-text-primary">Primary text</span>
<span class="text-text-secondary">Secondary text</span>
<span class="text-text-link">Link text</span>
<span class="text-text-linkHover">Link hover</span>
<span class="text-text-disabled">Disabled text</span>
```

### Semantic Text Colors
```html
<span class="text-primary-base">Primary text</span>
<span class="text-accent-base">Accent text</span>
<span class="text-positive-base">Success text</span>
<span class="text-negative-base">Error text</span>
<span class="text-info-base">Info text</span>
```

## 🔲 **Border Utilities**

### Border Colors
```html
<div class="border border-border-default">Default border</div>
<div class="border border-border-light">Light border</div>
<div class="border border-border-medium">Medium border</div>
<div class="border border-border-hover">Hover border</div>
<div class="border border-border-focus">Focus border</div>
<div class="border border-border-disabled">Disabled border</div>
```

### Semantic Border Colors
```html
<div class="border border-primary-base">Primary border</div>
<div class="border border-accent-base">Accent border</div>
<div class="border border-positive-base">Success border</div>
<div class="border border-negative-base">Error border</div>
```

## 🎯 **Usage Examples**

### Button Component
```html
<!-- Instead of CSS variables -->
<button style="background-color: var(--tokens-primary-base); color: white;">
  Old Way
</button>

<!-- Use utility classes -->
<button class="bg-primary-base text-white">
  New Way
</button>
```

### Card Component
```html
<div class="bg-background-secondary border border-border-default rounded-lg p-4">
  <h3 class="text-text-primary font-semibold mb-2">Card Title</h3>
  <p class="text-text-secondary">Card content with semantic colors</p>
  <button class="bg-primary-base text-white px-4 py-2 rounded hover:bg-primary-hover">
    Action
  </button>
</div>
```

### Form Elements
```html
<input 
  class="bg-background-primary border border-border-default text-text-primary 
         focus:border-border-focus rounded px-3 py-2"
  placeholder="Enter text..."
/>

<button class="bg-accent-base text-white px-4 py-2 rounded hover:bg-accent-hover">
  Submit
</button>
```

### Status Indicators
```html
<div class="bg-positive-subtle border border-positive-base text-positive-base p-3 rounded">
  ✅ Success message
</div>

<div class="bg-negative-subtle border border-negative-base text-negative-base p-3 rounded">
  ❌ Error message
</div>

<div class="bg-info-subtle border border-info-base text-info-base p-3 rounded">
  ℹ️ Info message
</div>
```

## 🌙 **Theme Mode Support**

All utility classes automatically adapt to the current theme mode:

```html
<!-- This will use light mode colors in light mode, dark mode colors in dark mode -->
<div class="bg-background-primary text-text-primary">
  Adaptive content
</div>

<!-- Custom modes work too -->
<div data-theme-mode="sepia">
  <div class="bg-background-primary text-text-primary">
    Sepia mode content
  </div>
</div>
```

## 🔄 **Migration from CSS Variables**

### Before (CSS Variables)
```tsx
<div style={{
  backgroundColor: 'var(--tokens-background-primary)',
  color: 'var(--tokens-text-primary)',
  borderColor: 'var(--tokens-border-default)'
}}>
  Content
</div>
```

### After (Utility Classes)
```tsx
<div className="bg-background-primary text-text-primary border-border-default">
  Content
</div>
```

## 🎨 **Available Token Categories**

- **Background**: `primary`, `secondary`, `surface`, `disabled`
- **Text**: `primary`, `secondary`, `link`, `linkHover`, `disabled`
- **Border**: `default`, `light`, `medium`, `hover`, `focus`, `disabled`
- **Primary**: `base`, `hover`, `active`, `subtle`, `background`
- **Accent**: `base`, `hover`, `active`, `border`, `subtle`, `background`
- **Positive**: `base`, `hover`, `active`, `border`, `subtle`, `background`
- **Negative**: `base`, `hover`, `active`, `border`, `subtle`, `background`
- **Info**: `base`, `hover`, `active`, `border`, `subtle`, `background`

## 🚀 **Benefits**

1. **Cleaner Code**: `bg-primary-base` vs `var(--tokens-primary-base)`
2. **Better DX**: IntelliSense support for class names
3. **Consistent**: Same naming as CSS variables but shorter
4. **Responsive**: Works with UnoCSS responsive prefixes
5. **Automatic**: Updates when themes change

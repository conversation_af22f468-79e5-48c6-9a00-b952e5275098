import { useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary py-16 px-8">
      <div className="max-w-md mx-auto space-y-16">
        {/* Header */}
        <div className="text-center py-8">
          <h1 className="text-text-primary text-2xl font-light mb-3">
            Next UI
          </h1>
          <p className="text-text-secondary text-base">
            {theme?.name} • {mode}
          </p>
        </div>

        {/* Controls */}
        <div className="py-4">
          <ThemeSelector />
        </div>

        {/* Colors */}
        <div className="py-4">
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-primary-base h-16 rounded-xl"></div>
            <div className="bg-accent-base h-16 rounded-xl"></div>
            <div className="bg-positive-base h-16 rounded-xl"></div>
            <div className="bg-negative-base h-16 rounded-xl"></div>
          </div>
        </div>

        {/* Buttons */}
        <div className="space-y-4 py-4">
          <button className="w-full bg-primary-base hover:bg-primary-hover text-white py-4 px-6 rounded-xl transition-colors font-medium">
            Primary Action
          </button>
          <button className="w-full bg-background-secondary hover:bg-background-surface border border-border-default text-text-primary py-4 px-6 rounded-xl transition-colors">
            Secondary Action
          </button>
        </div>

        {/* Messages */}
        <div className="space-y-4 py-4">
          <div className="bg-positive-subtle border-l-4 border-positive-base text-positive-base p-6 rounded-lg">
            <div className="font-semibold text-base">Success</div>
            <div className="opacity-75 mt-2 text-sm">
              Operation completed successfully
            </div>
          </div>
          <div className="bg-negative-subtle border-l-4 border-negative-base text-negative-base p-6 rounded-lg">
            <div className="font-semibold text-base">Error</div>
            <div className="opacity-75 mt-2 text-sm">Something went wrong</div>
          </div>
        </div>
      </div>
    </div>
  );
};

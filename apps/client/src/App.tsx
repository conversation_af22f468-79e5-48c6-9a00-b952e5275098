import { NuiButton, NuiTypography, useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary">
      {/* Header */}
      <header className="bg-background-surface border-b border-border-light">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <NuiTypography
                variant="title1"
                className="text-text-primary font-bold"
              >
                Next UI Design System
              </NuiTypography>
              <NuiTypography variant="caption1" className="text-text-secondary">
                Theme: {theme?.name} • Mode: {mode}
              </NuiTypography>
            </div>
            <div className="bg-primary-subtle border border-primary-base rounded-lg px-3 py-1">
              <span className="text-primary-base text-sm font-medium">
                v1.0.0
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Theme Controls */}
          <div className="lg:col-span-1 space-y-6">
            <div className="bg-background-surface border border-border-default rounded-xl p-6">
              <h2 className="text-text-primary font-semibold text-lg mb-4">
                Theme Controls
              </h2>
              <ThemeSelector />
            </div>

            <div className="bg-background-surface border border-border-default rounded-xl p-6">
              <h2 className="text-text-primary font-semibold text-lg mb-4">
                Quick Actions
              </h2>
              <div className="space-y-3">
                <NuiButton className="w-full bg-primary-base hover:bg-primary-hover text-white border-none py-3 px-4 rounded-lg transition-colors">
                  Primary Action
                </NuiButton>
                <button className="w-full bg-background-secondary hover:bg-background-primary border border-border-default text-text-primary py-3 px-4 rounded-lg transition-colors">
                  Secondary Action
                </button>
                <button className="w-full bg-accent-base hover:bg-accent-hover text-white border-none py-3 px-4 rounded-lg transition-colors">
                  Accent Action
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Components Showcase */}
          <div className="lg:col-span-2 space-y-6">
            {/* Semantic Colors Grid */}
            <div className="bg-background-surface border border-border-default rounded-xl p-6">
              <h2 className="text-text-primary font-semibold text-lg mb-4">
                Semantic Colors
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button className="bg-primary-base hover:bg-primary-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
                  Primary
                </button>
                <button className="bg-accent-base hover:bg-accent-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
                  Accent
                </button>
                <button className="bg-positive-base hover:bg-positive-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
                  Success
                </button>
                <button className="bg-negative-base hover:bg-negative-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
                  Error
                </button>
                <button className="bg-info-base hover:bg-info-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
                  Info
                </button>
                <button className="bg-background-secondary hover:bg-background-primary border border-border-default text-text-primary p-3 rounded-lg transition-colors text-sm font-medium">
                  Neutral
                </button>
                <button className="bg-text-primary hover:bg-text-secondary text-background-primary p-3 rounded-lg transition-colors text-sm font-medium">
                  Inverse
                </button>
                <button className="border-2 border-dashed border-border-default text-text-secondary p-3 rounded-lg hover:border-border-hover transition-colors text-sm font-medium">
                  Outline
                </button>
              </div>
            </div>

            {/* Status Messages */}
            <div className="bg-background-surface border border-border-default rounded-xl p-6">
              <h2 className="text-text-primary font-semibold text-lg mb-4">
                Status Messages
              </h2>
              <div className="space-y-3">
                <div className="bg-positive-subtle border border-positive-base text-positive-base p-4 rounded-lg flex items-center gap-3">
                  <span className="text-lg">✅</span>
                  <div>
                    <div className="font-medium">Success!</div>
                    <div className="text-sm opacity-80">
                      Your changes have been saved successfully.
                    </div>
                  </div>
                </div>
                <div className="bg-negative-subtle border border-negative-base text-negative-base p-4 rounded-lg flex items-center gap-3">
                  <span className="text-lg">❌</span>
                  <div>
                    <div className="font-medium">Error occurred</div>
                    <div className="text-sm opacity-80">
                      Please check your input and try again.
                    </div>
                  </div>
                </div>
                <div className="bg-info-subtle border border-info-base text-info-base p-4 rounded-lg flex items-center gap-3">
                  <span className="text-lg">ℹ️</span>
                  <div>
                    <div className="font-medium">Information</div>
                    <div className="text-sm opacity-80">
                      This feature is currently in beta testing.
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Utility Classes Demo */}
            <div className="bg-background-surface border border-border-default rounded-xl p-6">
              <h2 className="text-text-primary font-semibold text-lg mb-4">
                Utility Classes Reference
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <h3 className="font-medium text-text-primary">Backgrounds</h3>
                  <div className="space-y-1 text-text-secondary">
                    <div>bg-background-primary</div>
                    <div>bg-background-secondary</div>
                    <div>bg-primary-base</div>
                    <div>bg-accent-base</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-text-primary">Text Colors</h3>
                  <div className="space-y-1 text-text-secondary">
                    <div>text-text-primary</div>
                    <div>text-text-secondary</div>
                    <div>text-primary-base</div>
                    <div>text-accent-base</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-text-primary">Borders</h3>
                  <div className="space-y-1 text-text-secondary">
                    <div>border-border-default</div>
                    <div>border-border-light</div>
                    <div>border-primary-base</div>
                    <div>border-accent-base</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-text-primary">States</h3>
                  <div className="space-y-1 text-text-secondary">
                    <div>hover:bg-primary-hover</div>
                    <div>focus:border-border-focus</div>
                    <div>active:bg-primary-active</div>
                    <div>disabled:bg-background-disabled</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-background-surface border-t border-border-light mt-12">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between text-sm">
            <div className="text-text-secondary">
              Built with Next UI Design System • Powered by UnoCSS
            </div>
            <div className="flex items-center gap-4">
              <a
                href="#"
                className="text-text-link hover:text-text-linkHover transition-colors"
              >
                Documentation
              </a>
              <a
                href="#"
                className="text-text-link hover:text-text-linkHover transition-colors"
              >
                GitHub
              </a>
              <a
                href="http://localhost:5174/__unocss/"
                className="text-text-link hover:text-text-linkHover transition-colors"
              >
                UnoCSS Inspector
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

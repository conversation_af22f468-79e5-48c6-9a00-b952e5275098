import { useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary p-8">
      <div className="max-w-md mx-auto space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-text-primary text-lg font-light mb-1">Next UI</h1>
          <p className="text-text-secondary text-sm">
            {theme?.name} • {mode}
          </p>
        </div>

        {/* Controls */}
        <ThemeSelector />

        {/* Colors */}
        <div className="grid grid-cols-4 gap-2">
          <div className="bg-primary-base h-8 rounded"></div>
          <div className="bg-accent-base h-8 rounded"></div>
          <div className="bg-positive-base h-8 rounded"></div>
          <div className="bg-negative-base h-8 rounded"></div>
        </div>

        {/* Messages */}
        <div className="space-y-2">
          <div className="bg-positive-subtle border-l-2 border-positive-base text-positive-base p-2 text-sm">
            Success
          </div>
          <div className="bg-negative-subtle border-l-2 border-negative-base text-negative-base p-2 text-sm">
            Error
          </div>
        </div>
      </div>
    </div>
  );
};

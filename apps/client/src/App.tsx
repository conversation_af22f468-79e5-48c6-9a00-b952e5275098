import { useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary p-12">
      <div className="max-w-sm mx-auto space-y-12">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-text-primary text-xl font-light mb-2">Next UI</h1>
          <p className="text-text-secondary text-sm">
            {theme?.name} • {mode}
          </p>
        </div>

        {/* Controls */}
        <ThemeSelector />

        {/* Colors */}
        <div className="grid grid-cols-4 gap-3">
          <div className="bg-primary-base h-12 rounded-lg"></div>
          <div className="bg-accent-base h-12 rounded-lg"></div>
          <div className="bg-positive-base h-12 rounded-lg"></div>
          <div className="bg-negative-base h-12 rounded-lg"></div>
        </div>

        {/* Buttons */}
        <div className="space-y-3">
          <button className="w-full bg-primary-base hover:bg-primary-hover text-white p-3 rounded-lg transition-colors text-sm font-medium">
            Primary Action
          </button>
          <button className="w-full bg-background-secondary hover:bg-background-surface border border-border-default text-text-primary p-3 rounded-lg transition-colors text-sm">
            Secondary Action
          </button>
        </div>

        {/* Messages */}
        <div className="space-y-3">
          <div className="bg-positive-subtle border-l-4 border-positive-base text-positive-base p-4 rounded text-sm">
            <div className="font-medium">Success</div>
            <div className="opacity-75 mt-1">
              Operation completed successfully
            </div>
          </div>
          <div className="bg-negative-subtle border-l-4 border-negative-base text-negative-base p-4 rounded text-sm">
            <div className="font-medium">Error</div>
            <div className="opacity-75 mt-1">Something went wrong</div>
          </div>
        </div>
      </div>
    </div>
  );
};

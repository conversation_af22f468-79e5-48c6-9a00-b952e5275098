import { NuiButton, NuiTypography, useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div
      className="p-6 max-w-md mx-auto h-screen"
      style={{
        backgroundColor: "var(--tokens-background-primary)",
        color: "var(--tokens-text-primary)",
      }}
    >
      <div className="mb-6">
        <NuiTypography
          variant="title1"
          style={{ color: "var(--tokens-text-primary)" }}
        >
          Next UI
        </NuiTypography>
        <NuiTypography
          variant="caption1"
          style={{ color: "var(--tokens-text-secondary)" }}
        >
          {`${theme?.name} • ${mode}`}
        </NuiTypography>
      </div>

      <div className="mb-6">
        <NuiButton
          className="w-full"
          style={{
            backgroundColor: "var(--tokens-primary-base)",
            color: "white",
            border: "none",
            padding: "12px 24px",
            borderRadius: "8px",
            cursor: "pointer",
          }}
        >
          Primary Button (CSS Variables)
        </NuiButton>
      </div>

      <ThemeSelector />
    </div>
  );
};

import { useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary py-12 px-6">
      <div className="max-w-sm mx-auto space-y-10">
        {/* Header */}
        <div className="text-center py-4">
          <h1 className="text-text-primary text-xl font-light mb-2">Next UI</h1>
          <p className="text-text-secondary text-sm">
            {theme?.name} • {mode}
          </p>
        </div>

        {/* Controls */}
        <div>
          <ThemeSelector />
        </div>

        {/* Colors */}
        <div>
          <div className="grid grid-cols-4 gap-2">
            <div className="space-y-1">
              <div className="bg-primary-base h-10 rounded-lg"></div>
              <div className="text-text-secondary text-xs font-mono">
                primary
              </div>
            </div>
            <div className="space-y-1">
              <div className="bg-accent-base h-10 rounded-lg"></div>
              <div className="text-text-secondary text-xs font-mono">
                accent
              </div>
            </div>
            <div className="space-y-1">
              <div className="bg-positive-base h-10 rounded-lg"></div>
              <div className="text-text-secondary text-xs font-mono">
                success
              </div>
            </div>
            <div className="space-y-1">
              <div className="bg-negative-base h-10 rounded-lg"></div>
              <div className="text-text-secondary text-xs font-mono">error</div>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="space-y-3">
          <button className="w-full bg-primary-base hover:bg-primary-hover text-white py-3 px-4 rounded-lg transition-colors text-sm font-medium">
            Primary Action
          </button>
          <button className="w-full bg-background-secondary hover:bg-background-surface border border-border-default text-text-primary py-3 px-4 rounded-lg transition-colors text-sm">
            Secondary Action
          </button>
        </div>

        {/* Messages */}
        <div className="space-y-3">
          <div className="bg-positive-subtle border-l-3 border-positive-base text-positive-base p-4 rounded text-sm">
            <div className="font-medium">Success</div>
            <div className="opacity-75 mt-1 text-xs">bg-positive-subtle</div>
          </div>
          <div className="bg-negative-subtle border-l-3 border-negative-base text-negative-base p-4 rounded text-sm">
            <div className="font-medium">Error</div>
            <div className="opacity-75 mt-1 text-xs">bg-negative-subtle</div>
          </div>
        </div>

        {/* Utility Examples */}
        <div className="space-y-2">
          <div className="text-text-secondary text-xs font-mono">
            text-text-secondary
          </div>
          <div className="text-primary-base text-xs font-mono">
            text-primary-base
          </div>
          <div className="border border-border-default p-2 rounded text-xs font-mono text-text-secondary">
            border-border-default
          </div>
        </div>
      </div>
    </div>
  );
};

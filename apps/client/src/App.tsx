import { NuiButton, NuiTypography, useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="p-6 max-w-md mx-auto h-screen bg-background-primary text-text-primary">
      <div className="mb-6">
        <NuiTypography variant="title1" className="text-text-primary">
          Next UI
        </NuiTypography>
        <NuiTypography variant="caption1" className="text-text-secondary">
          {`${theme?.name} • ${mode}`}
        </NuiTypography>
      </div>

      <div className="mb-6 space-y-4">
        <NuiButton className="w-full bg-primary-base text-white border-none p-3 rounded-lg cursor-pointer">
          Primary Button (Utility Classes)
        </NuiButton>

        <div className="p-4 bg-background-secondary border border-border-default rounded-lg">
          <h3 className="text-text-primary font-semibold mb-2">
            Theme Utilities Demo
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-text-secondary">Background:</span>
              <span className="text-text-primary">bg-background-primary</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Text:</span>
              <span className="text-text-primary">text-text-primary</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Primary:</span>
              <span className="text-text-primary">bg-primary-base</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Border:</span>
              <span className="text-text-primary">border-border-default</span>
            </div>
          </div>
        </div>
      </div>

      <ThemeSelector />
    </div>
  );
};

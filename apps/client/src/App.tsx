import { NuiButton, NuiTypography, useTheme } from "@next-ui/core";

import { ThemeSelector, UtilityTest } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="p-6 max-w-md mx-auto h-screen bg-background-primary text-text-primary">
      <div className="mb-6">
        <NuiTypography variant="title1" className="text-text-primary">
          Next UI
        </NuiTypography>
        <NuiTypography variant="caption1" className="text-text-secondary">
          {`${theme?.name} • ${mode}`}
        </NuiTypography>
      </div>

      <div className="mb-6 space-y-4">
        <NuiButton className="w-full bg-primary-base text-white border-none p-3 rounded-lg cursor-pointer">
          Primary Button (Utility Classes)
        </NuiButton>

        <div className="p-4 bg-background-secondary border border-border-default rounded-lg">
          <h3 className="text-text-primary font-semibold mb-2">
            Theme Utilities Demo
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-text-secondary">Background:</span>
              <span className="text-text-primary">bg-background-primary</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Text:</span>
              <span className="text-text-primary">text-text-primary</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Primary:</span>
              <span className="text-text-primary">bg-primary-base</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Border:</span>
              <span className="text-text-primary">border-border-default</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <button className="bg-accent-base text-white p-2 rounded text-sm hover:bg-accent-hover">
            Accent
          </button>
          <button className="bg-positive-base text-white p-2 rounded text-sm hover:bg-positive-hover">
            Success
          </button>
          <button className="bg-negative-base text-white p-2 rounded text-sm hover:bg-negative-hover">
            Error
          </button>
          <button className="bg-info-base text-white p-2 rounded text-sm hover:bg-info-hover">
            Info
          </button>
        </div>

        <div className="space-y-2">
          <div className="bg-positive-subtle border border-positive-base text-positive-base p-2 rounded text-sm">
            ✅ Success message with utility classes
          </div>
          <div className="bg-negative-subtle border border-negative-base text-negative-base p-2 rounded text-sm">
            ❌ Error message with utility classes
          </div>
          <div className="bg-info-subtle border border-info-base text-info-base p-2 rounded text-sm">
            ℹ️ Info message with utility classes
          </div>
        </div>
      </div>

      <ThemeSelector />

      <div className="mt-6">
        <UtilityTest />
      </div>
    </div>
  );
};

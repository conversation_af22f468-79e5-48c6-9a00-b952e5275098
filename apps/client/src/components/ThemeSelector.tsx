import { useTheme } from "@next-ui/core";

export const ThemeSelector = () => {
  const { themes, theme, mode, changeTheme, changeMode } = useTheme();

  return (
    <div className="space-y-6">
      {/* Theme Selector */}
      <div>
        <label className="block text-text-secondary text-sm mb-3">Theme</label>
        <select
          className="w-full bg-background-primary border border-border-light text-text-primary p-3 rounded focus:border-border-default focus:outline-none transition-colors"
          value={theme.name}
          onChange={(e) => changeTheme(e.target.value)}
        >
          {themes.map((themeOption) => (
            <option key={themeOption.name} value={themeOption.name}>
              {themeOption.name}
            </option>
          ))}
        </select>
      </div>

      {/* Mode Selector */}
      <div>
        <label className="block text-text-secondary text-sm mb-3">Mode</label>
        <select
          className="w-full bg-background-primary border border-border-light text-text-primary p-3 rounded focus:border-border-default focus:outline-none transition-colors"
          value={mode}
          onChange={(e) => changeMode(e.target.value)}
        >
          {Object.keys(theme.modes).map((modeOption) => (
            <option key={modeOption} value={modeOption}>
              {modeOption}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

import { useTheme } from "@next-ui/core";

export const ThemeSelector = () => {
  const { themes, theme, mode, changeTheme, changeMode } = useTheme();

  return (
    <div className="space-y-5">
      <div>
        <label className="block text-text-secondary text-sm mb-2">Theme</label>
        <select
          className="w-full bg-background-secondary border border-border-light text-text-primary py-3 px-3 text-sm rounded-lg focus:outline-none focus:border-border-default transition-colors"
          value={theme.name}
          onChange={(e) => changeTheme(e.target.value)}
        >
          {themes.map((themeOption) => (
            <option key={themeOption.name} value={themeOption.name}>
              {themeOption.name.charAt(0).toUpperCase() +
                themeOption.name.slice(1)}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-text-secondary text-sm mb-2">Mode</label>
        <select
          className="w-full bg-background-secondary border border-border-light text-text-primary py-3 px-3 text-sm rounded-lg focus:outline-none focus:border-border-default transition-colors"
          value={mode}
          onChange={(e) => changeMode(e.target.value)}
        >
          {Object.keys(theme.modes).map((modeOption) => (
            <option key={modeOption} value={modeOption}>
              {modeOption.charAt(0).toUpperCase() + modeOption.slice(1)}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

import { useTheme } from "@next-ui/core";

export const ThemeSelector = () => {
  const { themes, theme, mode, changeTheme, changeMode } = useTheme();

  return (
    <div className="space-y-4">
      {/* Theme Selector */}
      <div>
        <label className="block text-text-secondary text-sm font-medium mb-2">
          Theme
        </label>
        <select
          className="w-full bg-background-secondary border border-border-default text-text-primary p-3 rounded-lg focus:border-border-focus focus:outline-none transition-colors"
          value={theme.name}
          onChange={(e) => changeTheme(e.target.value)}
        >
          {themes.map((themeOption) => (
            <option key={themeOption.name} value={themeOption.name}>
              {themeOption.name.charAt(0).toUpperCase() +
                themeOption.name.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* Mode Selector */}
      <div>
        <label className="block text-text-secondary text-sm font-medium mb-2">
          Mode
        </label>
        <select
          className="w-full bg-background-secondary border border-border-default text-text-primary p-3 rounded-lg focus:border-border-focus focus:outline-none transition-colors"
          value={mode}
          onChange={(e) => changeMode(e.target.value)}
        >
          {Object.keys(theme.modes).map((modeOption) => (
            <option key={modeOption} value={modeOption}>
              {modeOption === "light"
                ? "☀️ Light"
                : modeOption === "dark"
                ? "🌙 Dark"
                : modeOption === "sepia"
                ? "📜 Sepia"
                : modeOption === "midnight"
                ? "🌌 Midnight"
                : `🎨 ${
                    modeOption.charAt(0).toUpperCase() + modeOption.slice(1)
                  }`}
            </option>
          ))}
        </select>
      </div>

      {/* Current Selection Info */}
      <div className="bg-primary-subtle border border-primary-base rounded-lg p-3">
        <div className="text-primary-base text-sm font-medium">
          {theme.name} • {mode}
        </div>
        <div className="text-primary-base text-xs opacity-75 mt-1">
          {Object.keys(theme.modes).length} modes available
        </div>
      </div>
    </div>
  );
};

import { useTheme } from "@next-ui/core";

export const ThemeSelector = () => {
  const { themes, theme, mode, changeTheme, changeMode } = useTheme();

  return (
    <div className="space-y-6">
      {/* Theme Selector */}
      <div>
        <label className="block text-text-secondary text-sm font-medium mb-2">
          Theme
        </label>
        <div className="grid grid-cols-1 gap-2">
          {themes.map((themeOption) => (
            <button
              key={themeOption.name}
              onClick={() => changeTheme(themeOption.name)}
              className={`p-3 rounded-lg border text-left transition-all ${
                theme.name === themeOption.name
                  ? "bg-primary-base text-white border-primary-base"
                  : "bg-background-secondary hover:bg-background-primary border-border-default text-text-primary"
              }`}
            >
              <div className="font-medium">
                {themeOption.name.charAt(0).toUpperCase() +
                  themeOption.name.slice(1)}
              </div>
              <div className="text-xs opacity-75 mt-1">
                {Object.keys(themeOption.modes).length} modes available
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Mode Selector */}
      <div>
        <label className="block text-text-secondary text-sm font-medium mb-2">
          Mode
        </label>
        <div className="grid grid-cols-1 gap-2">
          {Object.keys(theme.modes).map((modeOption) => (
            <button
              key={modeOption}
              onClick={() => changeMode(modeOption)}
              className={`p-3 rounded-lg border text-left transition-all ${
                mode === modeOption
                  ? "bg-accent-base text-white border-accent-base"
                  : "bg-background-secondary hover:bg-background-primary border-border-default text-text-primary"
              }`}
            >
              <div className="font-medium">
                {modeOption.charAt(0).toUpperCase() + modeOption.slice(1)}
              </div>
              <div className="text-xs opacity-75 mt-1">
                {modeOption === "light"
                  ? "☀️ Light theme"
                  : modeOption === "dark"
                  ? "🌙 Dark theme"
                  : modeOption === "sepia"
                  ? "📜 Sepia theme"
                  : modeOption === "midnight"
                  ? "🌌 Midnight theme"
                  : "🎨 Custom theme"}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Selection Info */}
      <div className="bg-primary-subtle border border-primary-base rounded-lg p-4">
        <div className="text-primary-base text-sm font-medium mb-1">
          Current Selection
        </div>
        <div className="text-primary-base text-xs">
          Theme: <span className="font-medium">{theme.name}</span> • Mode:{" "}
          <span className="font-medium">{mode}</span>
        </div>
      </div>
    </div>
  );
};

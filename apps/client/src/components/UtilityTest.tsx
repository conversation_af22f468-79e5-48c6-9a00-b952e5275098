export const UtilityTest = () => {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold text-text-primary">Utility Classes Test</h2>
      
      {/* Test background utilities */}
      <div className="space-y-2">
        <h3 className="font-semibold text-text-primary">Background Utilities</h3>
        <div className="bg-background-primary p-2 rounded">bg-background-primary</div>
        <div className="bg-background-secondary p-2 rounded">bg-background-secondary</div>
        <div className="bg-primary-base text-white p-2 rounded">bg-primary-base</div>
        <div className="bg-accent-base text-white p-2 rounded">bg-accent-base</div>
      </div>

      {/* Test text utilities */}
      <div className="space-y-2">
        <h3 className="font-semibold text-text-primary">Text Utilities</h3>
        <div className="text-text-primary">text-text-primary</div>
        <div className="text-text-secondary">text-text-secondary</div>
        <div className="text-primary-base">text-primary-base</div>
        <div className="text-accent-base">text-accent-base</div>
      </div>

      {/* Test border utilities */}
      <div className="space-y-2">
        <h3 className="font-semibold text-text-primary">Border Utilities</h3>
        <div className="border border-border-default p-2 rounded">border-border-default</div>
        <div className="border border-primary-base p-2 rounded">border-primary-base</div>
        <div className="border border-accent-base p-2 rounded">border-accent-base</div>
      </div>

      {/* Test semantic colors */}
      <div className="space-y-2">
        <h3 className="font-semibold text-text-primary">Semantic Colors</h3>
        <div className="bg-positive-base text-white p-2 rounded">bg-positive-base</div>
        <div className="bg-negative-base text-white p-2 rounded">bg-negative-base</div>
        <div className="bg-info-base text-white p-2 rounded">bg-info-base</div>
      </div>
    </div>
  );
};

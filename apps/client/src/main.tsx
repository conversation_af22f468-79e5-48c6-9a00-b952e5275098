import {
  NuiProvider,
  brandTheme,
  neutralTheme,
  minimalTheme,
  customColorTheme,
  customModeTheme,
  customDesignTheme,
  comprehensiveTheme,
} from "@next-ui/core";
import { createRoot } from "react-dom/client";

import "@unocss/reset/tailwind-compat.css";
import "virtual:uno.css";

import { App } from "./App";

createRoot(document.getElementById("root")!).render(
  <NuiProvider
    themes={[
      brandTheme,
      neutralTheme,
      minimalTheme,
      customColorTheme,
      customModeTheme,
      customDesignTheme,
      comprehensiveTheme,
    ]}
  >
    <App />
  </NuiProvider>
);

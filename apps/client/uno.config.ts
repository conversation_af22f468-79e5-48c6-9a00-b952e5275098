import { presetNextUI } from "@next-ui/uno-preset";
import {
  brandTheme,
  neutralTheme,
  minimalTheme,
  customColorTheme,
  customModeTheme,
  customDesignTheme,
  comprehensiveTheme,
} from "@next-ui/styles";
import { defineConfig, presetMini, transformerVariantGroup } from "unocss";

export default defineConfig({
  presets: [
    presetMini(),
    presetNextUI({
      themes: [
        neutralTheme,
        brandTheme,
        minimalTheme,
        customColorTheme,
        customModeTheme,
        customDesignTheme,
        comprehensiveTheme,
      ],
      includeCSSVars: true,
    }),
  ],
  // Enable the variant group transformer for grouping CSS variants
  transformers: [transformerVariantGroup()],
  // Specify the content files to include in the CSS generation pipeline
  content: {
    pipeline: {
      include: [
        // Include files for UnoCSS processing
        /\.(jsx?|tsx?|mdx?|html)($|\?)/,
      ],
    },
  },
});

import { presetNextUI } from "@next-ui/uno-preset";
import { brandTheme, neutralTheme } from "@next-ui/styles";
import { defineConfig, presetMini, transformerVariantGroup } from "unocss";

export default defineConfig({
  presets: [
    preset<PERSON>ini(),
    presetNextUI({
      themes: [neutralTheme, brandTheme],
      includeCSSVars: true,
    }),
  ],
  // Enable the variant group transformer for grouping CSS variants
  transformers: [transformerVariantGroup()],
  // Specify the content files to include in the CSS generation pipeline
  content: {
    pipeline: {
      include: [
        // Include files for UnoCSS processing
        /\.(jsx?|tsx?|mdx?|html)($|\?)/,
      ],
    },
  },
});

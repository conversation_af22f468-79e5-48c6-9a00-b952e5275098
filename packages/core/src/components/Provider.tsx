import type { NuiTheme } from "@next-ui/styles";
import { createContext, useEffect, useState } from "react";

import type { NuiContextProps, NuiProviderProps } from "../types";

export const NuiContext = createContext<NuiContextProps | undefined>(undefined);

export const NuiProvider = ({
  themes,
  initialTheme,
  initialMode,
  children,
}: NuiProviderProps) => {
  const [theme, setTheme] = useState<NuiTheme>(
    () => themes.find((t) => t.name === initialTheme) || themes[0]
  );

  const [mode, setMode] = useState<string>(() =>
    initialMode && initialMode in theme.modes
      ? initialMode
      : Object.keys(theme.modes)[0]
  );

  useEffect(() => {
    document.documentElement.setAttribute("data-theme", theme.name);
    document.documentElement.setAttribute("data-theme-mode", mode);
  }, [theme.name, mode]);

  const changeTheme = (themeName: string) => {
    const newTheme = themes.find((t) => t.name === themeName);
    if (newTheme) {
      setTheme(newTheme);
      setMode(Object.keys(newTheme.modes)[0]);
    }
  };

  const changeMode = (modeName: string) => {
    if (modeName in theme.modes) {
      setMode(modeName);
    }
  };

  const contextValue = {
    themes,
    theme,
    mode,
    changeTheme,
    changeMode,
  };

  return (
    <NuiContext.Provider value={contextValue}>{children}</NuiContext.Provider>
  );
};

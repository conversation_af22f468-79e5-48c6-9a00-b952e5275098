import type { NuiTheme } from "@next-ui/styles";
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import type { NuiContextProps, NuiProviderProps } from "../types";

export const NuiContext = createContext<NuiContextProps | undefined>(undefined);

export const NuiProvider = ({
  themes,
  initialTheme,
  initialMode,
  rootElement = "root",
  children,
}: NuiProviderProps) => {
  // Initialize with first theme if no initial theme specified
  const [theme, setTheme] = useState<NuiTheme>(() => {
    if (initialTheme) {
      return themes.find((t) => t.name === initialTheme) || themes[0];
    }
    return themes[0];
  });

  // Initialize with first mode of the theme if no initial mode specified
  const [mode, setMode] = useState<string>(() => {
    if (initialMode && initialMode in theme.modes) {
      return initialMode;
    }
    return Object.keys(theme.modes)[0];
  });

  // Apply theme and mode to DOM
  useEffect(() => {
    const root =
      rootElement === "root"
        ? document.documentElement
        : document.getElementById(rootElement);

    if (!root) return;

    // Set theme data attribute
    root.setAttribute("data-theme", theme.name.toLowerCase());

    // Set mode data attribute
    root.setAttribute("data-theme-mode", mode);

    // Add transition class for smooth theme changes
    root.classList.add("theme-transition");
  }, [theme, mode, rootElement]);

  const changeTheme = useCallback(
    (themeName: string) => {
      const newTheme = themes.find((t) => t.name === themeName);
      if (newTheme) {
        setTheme(newTheme);
        // Reset to first mode of new theme
        setMode(Object.keys(newTheme.modes)[0]);
      }
    },
    [themes],
  );

  const changeMode = useCallback(
    (newMode: string) => {
      if (newMode in theme.modes) {
        setMode(newMode);
      }
    },
    [theme],
  );

  const contextValue = useMemo(
    () => ({
      themes,
      theme,
      mode,
      changeTheme,
      changeMode,
    }),
    [themes, theme, mode, changeTheme, changeMode],
  );

  return (
    <NuiContext.Provider value={contextValue}>{children}</NuiContext.Provider>
  );
};

import type { NuiTheme } from "@next-ui/styles";
import type { ReactNode } from "react";

/**
 * Defines the props for the `NuiProvider` component.
 */
export type NuiProviderProps = {
  /** A collection of themes to be used by the provider. */
  themes: NuiTheme[];
  /** The initial theme to apply. If not provided, defaults to the first theme. */
  initialTheme?: string;
  /** The initial mode to apply. If not provided, defaults to the first available mode. */
  initialMode?: string;
  /** The child components that will consume the theme context. */
  children: ReactNode;
};

/**
 * Defines the shape of the context value provided by `NuiProvider`.
 */
export type NuiContextProps = {
  /** A collection of available themes. */
  themes: NuiTheme[];
  /** The currently active theme. */
  theme: NuiTheme;
  /** The currently active mode (e.g., "light" or "dark"). */
  mode: string;
  /** A function to update the current theme. */
  changeTheme: (themeName: string) => void;
  /** A function to update the current mode. */
  changeMode: (modeName: string) => void;
};

#!/usr/bin/env node

import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

// Import theme utilities and themes
import { brandTheme } from "../src/themes/brand.js";
import { neutralTheme } from "../src/themes/neutral.js";
import {
  generateCompleteThemeCSS,
  generateThemeCSS,
} from "../src/utils/build-css.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Output directory for generated CSS
const outputDir = join(__dirname, "../dist/css");

// Ensure output directory exists
if (!existsSync(outputDir)) {
  mkdirSync(outputDir, { recursive: true });
}

/**
 * Generate CSS files for individual themes
 */
function generateIndividualThemeFiles() {
  const themes = [brandTheme, neutralTheme];

  themes.forEach((theme) => {
    const modes = Object.keys(theme.modes);

    modes.forEach((mode) => {
      const css = generateThemeCSS(theme, mode, ":root");
      const filename = `${theme.name.toLowerCase()}-${mode}.css`;
      const filepath = join(outputDir, filename);

      writeFileSync(filepath, css, "utf8");
      console.log(`✓ Generated ${filename}`);
    });
  });
}

/**
 * Generate a combined CSS file with all themes and modes
 */
function generateCombinedThemeFile() {
  const themes = [brandTheme, neutralTheme];
  const css = generateCompleteThemeCSS(themes);
  const filepath = join(outputDir, "themes.css");

  writeFileSync(filepath, css, "utf8");
  console.log(`✓ Generated themes.css`);
}

/**
 * Generate CSS variables as JSON for runtime usage
 */
function generateThemeVariablesJSON() {
  const themes = [brandTheme, neutralTheme];

  const themeData = themes.map((theme) => {
    const modes = Object.keys(theme.modes);
    const modeData = modes.reduce((acc, mode) => {
      const selectedMode = theme.modes[mode as keyof typeof theme.modes];
      acc[mode] = {
        colors: theme.colors,
        tokens: selectedMode,
        designTokens: theme.designTokens,
      };
      return acc;
    }, {} as Record<string, any>);

    return {
      name: theme.name,
      modes: modeData,
    };
  });

  const jsonContent = JSON.stringify(themeData, null, 2);
  const filepath = join(outputDir, "themes.json");

  writeFileSync(filepath, jsonContent, "utf8");
  console.log(`✓ Generated themes.json`);
}

/**
 * Main execution
 */
function main() {
  console.log("🎨 Generating theme CSS variables...");

  try {
    generateIndividualThemeFiles();
    generateCombinedThemeFile();
    generateThemeVariablesJSON();

    console.log("\n✅ Theme CSS generation completed successfully!");
    console.log(`📁 Output directory: ${outputDir}`);
  } catch (error) {
    console.error("❌ Error generating theme CSS:", error);
    process.exit(1);
  }
}

// Run the script
main();

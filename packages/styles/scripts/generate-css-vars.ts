#!/usr/bin/env node

import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

// Import theme utilities and themes
import { brandTheme } from "../src/themes/brand.js";
import { neutralTheme } from "../src/themes/neutral.js";
import { generateCompleteThemeCSS } from "../src/utils/build-css.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Output directory for generated CSS
const outputDir = join(__dirname, "../dist/css");

// Ensure output directory exists
if (!existsSync(outputDir)) {
  mkdirSync(outputDir, { recursive: true });
}

/**
 * Main execution
 */
function main() {
  console.log("🎨 Generating theme CSS variables...");

  try {
    const themes = [brandTheme, neutralTheme];

    // Generate combined CSS file with all themes and modes
    const css = generateCompleteThemeCSS(themes);
    const filepath = join(outputDir, "themes.css");
    writeFileSync(filepath, css, "utf8");
    console.log(`✓ Generated themes.css`);

    console.log("\n✅ Theme CSS generation completed successfully!");
    console.log(`📁 Output directory: ${outputDir}`);
  } catch (error) {
    console.error("❌ Error generating theme CSS:", error);
    process.exit(1);
  }
}

// Run the script
main();

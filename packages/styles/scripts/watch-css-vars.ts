#!/usr/bin/env node

import { watch } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";
import { debounce } from "lodash-es";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Directories to watch for changes
const WATCH_DIRS = [
  join(__dirname, "../src/themes"),
  join(__dirname, "../src/utils"),
];

// File extensions to watch
const WATCH_EXTENSIONS = [".ts", ".js", ".json"];

/**
 * Regenerates CSS variables when theme files change
 */
async function regenerateCSS() {
  console.log("🔄 Regenerating CSS variables...");
  
  try {
    // Dynamic import to avoid module loading issues
    const { execSync } = await import("child_process");
    
    execSync("bun run build:css", {
      cwd: join(__dirname, ".."),
      stdio: "inherit",
    });
    
    console.log("✅ CSS variables regenerated successfully!");
  } catch (error) {
    console.error("❌ Failed to regenerate CSS variables:", error);
  }
}

/**
 * Debounced version of regenerateCSS to avoid excessive rebuilds
 */
const debouncedRegenerate = debounce(regenerateCSS, 500);

/**
 * Checks if a file should trigger a rebuild
 */
function shouldRebuild(filename: string): boolean {
  return WATCH_EXTENSIONS.some(ext => filename.endsWith(ext));
}

/**
 * Sets up file watchers for theme directories
 */
function setupWatchers() {
  console.log("👀 Watching for theme changes...");
  
  WATCH_DIRS.forEach(dir => {
    try {
      const watcher = watch(dir, { recursive: true }, (eventType, filename) => {
        if (filename && shouldRebuild(filename)) {
          console.log(`📝 ${eventType}: ${filename}`);
          debouncedRegenerate();
        }
      });
      
      console.log(`✅ Watching: ${dir}`);
      
      // Handle watcher errors
      watcher.on("error", (error) => {
        console.error(`❌ Watcher error for ${dir}:`, error);
      });
      
    } catch (error) {
      console.warn(`⚠️  Could not watch ${dir}:`, error);
    }
  });
}

/**
 * Main function
 */
function main() {
  console.log("🎨 Next UI CSS Variables Watcher");
  console.log("================================");
  
  // Initial build
  regenerateCSS();
  
  // Setup watchers
  setupWatchers();
  
  console.log("\n🚀 Watcher is running. Press Ctrl+C to stop.");
  
  // Handle graceful shutdown
  process.on("SIGINT", () => {
    console.log("\n👋 Stopping CSS variables watcher...");
    process.exit(0);
  });
  
  process.on("SIGTERM", () => {
    console.log("\n👋 Stopping CSS variables watcher...");
    process.exit(0);
  });
}

// Run the watcher
main();

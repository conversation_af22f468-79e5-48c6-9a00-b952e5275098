/**
 * TypeScript declarations for CSS variables
 * This provides IntelliSense support for CSS variables in TypeScript
 */

declare module "csstype" {
  interface Properties {
    // Color CSS Variables
    "--colors-slate-50"?: string;
    "--colors-slate-100"?: string;
    "--colors-slate-200"?: string;
    "--colors-slate-300"?: string;
    "--colors-slate-400"?: string;
    "--colors-slate-500"?: string;
    "--colors-slate-600"?: string;
    "--colors-slate-700"?: string;
    "--colors-slate-800"?: string;
    "--colors-slate-900"?: string;
    "--colors-slate-950"?: string;
    
    "--colors-blue-50"?: string;
    "--colors-blue-100"?: string;
    "--colors-blue-200"?: string;
    "--colors-blue-300"?: string;
    "--colors-blue-400"?: string;
    "--colors-blue-500"?: string;
    "--colors-blue-600"?: string;
    "--colors-blue-700"?: string;
    "--colors-blue-800"?: string;
    "--colors-blue-900"?: string;
    "--colors-blue-950"?: string;
    
    // Token CSS Variables
    "--tokens-primary-base"?: string;
    "--tokens-primary-hover"?: string;
    "--tokens-primary-active"?: string;
    "--tokens-primary-subtle"?: string;
    "--tokens-primary-background"?: string;
    "--tokens-primary-border"?: string;
    
    "--tokens-accent-base"?: string;
    "--tokens-accent-hover"?: string;
    "--tokens-accent-active"?: string;
    "--tokens-accent-subtle"?: string;
    "--tokens-accent-background"?: string;
    "--tokens-accent-border"?: string;
    
    "--tokens-text-primary"?: string;
    "--tokens-text-secondary"?: string;
    "--tokens-text-link"?: string;
    "--tokens-text-linkHover"?: string;
    "--tokens-text-disabled"?: string;
    
    "--tokens-background-primary"?: string;
    "--tokens-background-secondary"?: string;
    "--tokens-background-surface"?: string;
    "--tokens-background-disabled"?: string;
    
    "--tokens-border-default"?: string;
    "--tokens-border-light"?: string;
    "--tokens-border-medium"?: string;
    "--tokens-border-hover"?: string;
    "--tokens-border-focus"?: string;
    "--tokens-border-disabled"?: string;
    
    // Design Token CSS Variables
    "--design-fontSize-xs"?: string;
    "--design-fontSize-sm"?: string;
    "--design-fontSize-base"?: string;
    "--design-fontSize-lg"?: string;
    "--design-fontSize-xl"?: string;
    "--design-fontSize-2xl"?: string;
    "--design-fontSize-3xl"?: string;
    "--design-fontSize-4xl"?: string;
    
    "--design-spacing-xs"?: string;
    "--design-spacing-sm"?: string;
    "--design-spacing-md"?: string;
    "--design-spacing-lg"?: string;
    "--design-spacing-xl"?: string;
    
    "--design-radii-none"?: string;
    "--design-radii-xs"?: string;
    "--design-radii-sm"?: string;
    "--design-radii-md"?: string;
    "--design-radii-lg"?: string;
    "--design-radii-xl"?: string;
    "--design-radii-full"?: string;
    "--design-radii-circle"?: string;
    
    // Fallback Variables
    "--fallback-color"?: string;
    "--fallback-background"?: string;
    "--fallback-border"?: string;
    "--fallback-text"?: string;
    "--fallback-surface"?: string;
  }
}

/**
 * CSS Variable utility types
 */
export type CSSVariable = `--${string}`;

export type ColorVariable = 
  | `--colors-${string}`
  | `--tokens-${string}`
  | `--design-${string}`;

export type ThemeVariable = 
  | `--tokens-primary-${string}`
  | `--tokens-accent-${string}`
  | `--tokens-text-${string}`
  | `--tokens-background-${string}`
  | `--tokens-border-${string}`;

/**
 * Helper function to create CSS variable references with fallbacks
 */
export function cssVar(variable: CSSVariable, fallback?: string): string {
  return fallback ? `var(${variable}, ${fallback})` : `var(${variable})`;
}

/**
 * Type-safe CSS variable object
 */
export interface CSSVariables {
  [key: CSSVariable]: string;
}

/**
 * CSS variable categories for better organization
 */
export interface CSSVariableCategories {
  colors: Record<string, string>;
  tokens: Record<string, string>;
  design: Record<string, string>;
  fallbacks: Record<string, string>;
}

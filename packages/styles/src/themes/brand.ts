import { createTheme } from "../utils/theme";

export const brandTheme = createTheme(({ baseTheme, modes, createMode }) => ({
  name: "Brand",
  modes: {
    ...baseTheme.modes, // Keep light and dark modes
    // Add new custom modes
    sepia: createMode(modes.light, {
      primary: {
        base: "#8B4513",
        hover: "#A0522D",
        active: "#654321",
        subtle: "#DEB887",
        background: "#F5DEB3",
      },
      text: {
        primary: "#2F1B14",
        secondary: "#8B4513",
        link: "#A0522D",
        linkHover: "#654321",
        disabled: "#D2B48C",
      },
      background: {
        primary: "#FDF5E6",
        secondary: "#F5DEB3",
        surface: "#FAEBD7",
        disabled: "#E6D3A3",
      },
      border: {
        default: "#DEB887",
        light: "#F0E68C",
        medium: "#DAA520",
        hover: "#B8860B",
        focus: "#8B4513",
        disabled: "#D2B48C",
      },
    }),
    midnight: createMode(modes.dark, {
      primary: {
        base: "#6366F1",
        hover: "#8B5CF6",
        active: "#A78BFA",
        subtle: "#312E81",
        background: "#1E1B4B",
      },
      text: {
        primary: "#E0E7FF",
        secondary: "#A5B4FC",
        link: "#8B5CF6",
        linkHover: "#A78BFA",
        disabled: "#64748B",
      },
      background: {
        primary: "#0F0F23",
        secondary: "#1E1B4B",
        surface: "#312E81",
        disabled: "#1E293B",
      },
    }),
  },
}));

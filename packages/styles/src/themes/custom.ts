import { createTheme } from "../utils/theme";

/**
 * Example of a minimal theme that just extends the base theme
 */
export const minimalTheme = createTheme(() => ({
  name: "Minimal",
  // Everything else is automatically inherited from base theme
}));

/**
 * Example of a theme with custom color overrides
 */
export const customColorTheme = createTheme(({ baseTheme, colors }) => ({
  name: "CustomColor",
  colors: {
    ...baseTheme.colors,
    // Override specific colors
    blue: {
      ...colors.blue,
      500: "#FF6B6B", // Custom primary blue
      600: "#FF5252", // Custom hover blue
    },
  },
}));

/**
 * Example of a theme with custom modes
 */
export const customModeTheme = createTheme(({ baseTheme, modes }) => ({
  name: "CustomMode",
  modes: {
    ...baseTheme.modes,
    // Add a new custom mode
    sepia: {
      primary: {
        base: "#8B4513",
        hover: "#A0522D",
        active: "#654321",
        subtle: "#DEB887",
        background: "#F5DEB3",
      },
      text: {
        primary: "#2F1B14",
        secondary: "#8B4513",
        link: "#A0522D",
        linkHover: "#654321",
        disabled: "#D2B48C",
      },
      background: {
        primary: "#FDF5E6",
        secondary: "#F5DEB3",
        surface: "#FAEBD7",
        disabled: "#E6D3A3",
      },
      border: {
        default: "#DEB887",
        light: "#F0E68C",
        medium: "#DAA520",
        hover: "#B8860B",
        focus: "#8B4513",
        disabled: "#D2B48C",
      },
    },
  },
}));

/**
 * Example of a theme with custom design tokens
 */
export const customDesignTheme = createTheme(({ baseTheme }) => ({
  name: "CustomDesign",
  designTokens: {
    ...baseTheme.designTokens,
    // Override specific design tokens
    fontSize: {
      ...baseTheme.designTokens.fontSize,
      base: "1rem", // Larger base font size
      lg: "1.25rem",
      xl: "1.5rem",
    },
    spacing: {
      ...baseTheme.designTokens.spacing,
      base: "1rem", // Larger base spacing
      md: "2rem",
      lg: "4rem",
    },
  },
}));

/**
 * Example of a comprehensive custom theme
 */
export const comprehensiveTheme = createTheme(({ baseTheme, colors, modes }) => ({
  name: "Comprehensive",
  colors: {
    ...baseTheme.colors,
    // Add custom brand colors
    brand: {
      50: "#FFF5F5",
      100: "#FED7D7",
      200: "#FEB2B2",
      300: "#FC8181",
      400: "#F56565",
      500: "#E53E3E", // Primary brand color
      600: "#C53030",
      700: "#9B2C2C",
      800: "#742A2A",
      900: "#4A1A1A",
      950: "#2D0A0A",
    },
  },
  modes: {
    light: {
      ...modes.light,
      // Override primary to use brand color
      primary: {
        base: "#E53E3E",
        hover: "#C53030",
        active: "#9B2C2C",
        subtle: "#FEB2B2",
        background: "#FED7D7",
      },
    },
    dark: {
      ...modes.dark,
      // Override primary for dark mode
      primary: {
        base: "#F56565",
        hover: "#FC8181",
        active: "#FEB2B2",
        subtle: "#742A2A",
        background: "#4A1A1A",
      },
    },
  },
  designTokens: {
    ...baseTheme.designTokens,
    radii: {
      ...baseTheme.designTokens.radii,
      md: "0.75rem", // More rounded corners
      lg: "1rem",
      xl: "1.5rem",
    },
  },
}));

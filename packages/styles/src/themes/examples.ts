import { createTheme } from "../utils/theme";

/**
 * Example 1: Simple object approach (no base theme access needed)
 */
export const simpleTheme = createTheme({
  name: "Simple",
  // Just specify overrides - base theme is merged automatically
  colors: {
    blue: {
      500: "#FF6B6B", // Override primary blue
    },
  },
});

/**
 * Example 2: Function approach with base theme access
 */
export const advancedTheme = createTheme((baseTheme) => ({
  name: "Advanced",
  colors: {
    // Reference existing base theme colors
    brand: {
      500: baseTheme.colors.blue[600], // Use base blue as brand color
      600: baseTheme.colors.blue[700],
    },
  },
  modes: {
    // Create custom mode based on existing mode
    vintage: {
      ...baseTheme.modes.light,
      primary: {
        base: baseTheme.colors.amber[600],
        hover: baseTheme.colors.amber[700],
        active: baseTheme.colors.amber[800],
        subtle: baseTheme.colors.amber[100],
        background: baseTheme.colors.amber[50],
      },
    },
  },
  designTokens: {
    // Reference base design tokens
    fontSize: {
      ...baseTheme.designTokens.fontSize,
      base: "1.125rem", // Larger than base
    },
  },
}));

/**
 * Example 3: Theme that extends another theme's colors
 */
export const extendedTheme = createTheme((baseTheme) => ({
  name: "Extended",
  colors: {
    // Keep all base colors and add new ones
    ...baseTheme.colors,
    // Add custom color palette
    sunset: {
      50: "#FFF7ED",
      100: "#FFEDD5",
      200: "#FED7AA",
      300: "#FDBA74",
      400: "#FB923C",
      500: "#F97316", // Primary sunset
      600: "#EA580C",
      700: "#C2410C",
      800: "#9A3412",
      900: "#7C2D12",
      950: "#431407",
    },
  },
  modes: {
    // Use the new sunset colors in a custom mode
    sunset: {
      ...baseTheme.modes.light,
      primary: {
        base: "#F97316", // sunset-500
        hover: "#EA580C", // sunset-600
        active: "#C2410C", // sunset-700
        subtle: "#FED7AA", // sunset-200
        background: "#FFEDD5", // sunset-100
      },
      background: {
        primary: "#FFF7ED", // sunset-50
        secondary: "#FFEDD5", // sunset-100
        surface: "#FFFFFF",
        disabled: "#F3F4F6",
      },
    },
  },
}));

/**
 * Example 4: Theme that modifies existing modes
 */
export const modifiedTheme = createTheme((baseTheme) => ({
  name: "Modified",
  modes: {
    // Modify the existing light mode
    light: {
      ...baseTheme.modes.light,
      // Change primary colors while keeping everything else
      primary: {
        ...baseTheme.modes.light.primary,
        base: "#10B981", // Green instead of blue
        hover: "#059669",
        active: "#047857",
      },
    },
    // Keep dark mode as-is (inherited automatically)
    // Add new mode
    forest: {
      ...baseTheme.modes.dark,
      primary: {
        base: "#22C55E",
        hover: "#16A34A",
        active: "#15803D",
        subtle: "#166534",
        background: "#14532D",
      },
      background: {
        primary: "#052E16",
        secondary: "#14532D",
        surface: "#166534",
        disabled: "#374151",
      },
    },
  },
}));

/**
 * Example 5: Minimal theme using object approach
 */
export const minimalTheme = createTheme({
  name: "Minimal",
  // Everything else inherited from base theme
});

/**
 * Example 6: Theme with custom design tokens
 */
export const designTheme = createTheme((baseTheme) => ({
  name: "Design",
  designTokens: {
    // Keep base tokens and override specific ones
    ...baseTheme.designTokens,
    spacing: {
      ...baseTheme.designTokens.spacing,
      base: "1rem", // Larger base spacing
      lg: "3rem",
      xl: "5rem",
    },
    radii: {
      ...baseTheme.designTokens.radii,
      md: "1rem", // More rounded
      lg: "1.5rem",
      xl: "2rem",
    },
  },
}));

import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme } from "../types";

export const neutralTheme: NuiTheme = {
  name: "Neutral",
  colors,
  modes: {
    light: colorModes.light,
    dark: colorModes.dark,
  },
  designTokens,
  variantTokens,
  components: {},
};

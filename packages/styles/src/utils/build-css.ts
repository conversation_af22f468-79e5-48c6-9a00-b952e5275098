import type { NuiTheme } from "../types";
import { createCSSVarMap } from "./css";

/**
 * Generates CSS content with CSS variables for a given theme and mode.
 */
export const generateThemeCSS = (
  theme: NuiTheme,
  mode: string = "light",
  selector: string = ":root"
): string => {
  const selectedMode =
    theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;

  // Generate CSS variables for colors, tokens, and design tokens
  const colorVars = createCSSVarMap(theme.colors, "colors");
  const tokenVars = createCSSVarMap(selectedMode, "tokens");
  const designTokenVars = createCSSVarMap(theme.designTokens, "design");

  // Combine all variables
  const allVars = {
    ...colorVars,
    ...tokenVars,
    ...designTokenVars,
  };

  // Generate CSS content
  const cssVarDeclarations = Object.entries(allVars)
    .map(([varName, value]) => `  ${varName}: ${value};`)
    .join("\n");

  return `${selector} {\n${cssVarDeclarations}\n}`;
};

/**
 * Generates CSS content for all modes of a theme.
 */
export const generateThemeModesCSS = (theme: NuiTheme): string => {
  const modes = Object.keys(theme.modes);

  return modes
    .map((mode) => {
      const selector =
        mode === "light" ? ":root" : `[data-theme-mode="${mode}"]`;
      return generateThemeCSS(theme, mode, selector);
    })
    .join("\n\n");
};

/**
 * Generates CSS content for multiple themes with all their modes.
 */
export const generateMultiThemeCSS = (themes: NuiTheme[]): string => {
  return themes
    .map((theme, index) => {
      const themeSelector =
        index === 0 ? "" : `[data-theme="${theme.name.toLowerCase()}"]`;
      const modes = Object.keys(theme.modes);

      return modes
        .map((mode) => {
          let selector = ":root";

          if (themeSelector && mode === "light") {
            selector = themeSelector;
          } else if (themeSelector && mode !== "light") {
            selector = `${themeSelector}[data-theme-mode="${mode}"]`;
          } else if (!themeSelector && mode !== "light") {
            selector = `[data-theme-mode="${mode}"]`;
          }

          return generateThemeCSS(theme, mode, selector);
        })
        .join("\n\n");
    })
    .join("\n\n");
};

/**
 * Generates a complete CSS file with theme variables and utility classes.
 */
export const generateCompleteThemeCSS = (themes: NuiTheme[]): string => {
  const themeCSS = generateMultiThemeCSS(themes);

  const utilityCSS = `
/* Theme utility classes */
.theme-transition {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* CSS variable fallbacks */
:root {
  --fallback-color: #000000;
  --fallback-background: #ffffff;
  --fallback-border: #e5e5e5;
}
`;

  return `/* Auto-generated theme CSS variables */\n/* Generated at: ${new Date().toISOString()} */\n\n${themeCSS}\n${utilityCSS}`;
};

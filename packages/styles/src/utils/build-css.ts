import type { NuiTheme } from "../types";

/**
 * Converts a nested object into CSS custom properties.
 */
const createCSSVarMap = (
  source: Record<string, unknown>,
  prefix = ""
): Record<string, string> => {
  const result: Record<string, string> = {};

  const flattenToVars = (obj: Record<string, unknown>, path: string[] = []) => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = [...path, key];

      if (typeof value === "string") {
        const pathString = currentPath.join("-");
        const variableName = prefix
          ? `--${prefix}-${pathString}`
          : `--${pathString}`;
        result[variableName] = value;
      } else if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        flattenToVars(value as Record<string, unknown>, currentPath);
      }
    }
  };

  flattenToVars(source);
  return result;
};

/**
 * Generates a complete CSS file with all themes, modes, and utility classes.
 */
export const generateCompleteThemeCSS = (themes: NuiTheme[]): string => {
  const cssBlocks: string[] = [];

  // Generate CSS for each theme and mode combination
  themes.forEach((theme, themeIndex) => {
    const modes = Object.keys(theme.modes);
    const isDefaultTheme = themeIndex === 0;

    modes.forEach((mode) => {
      const selectedMode =
        theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;
      const isDefaultMode = mode === "light";

      // Determine CSS selector
      let selector = ":root";
      if (isDefaultTheme && !isDefaultMode) {
        selector = `[data-theme-mode="${mode}"]`;
      } else if (!isDefaultTheme && isDefaultMode) {
        selector = `[data-theme="${theme.name.toLowerCase()}"]`;
      } else if (!isDefaultTheme && !isDefaultMode) {
        selector = `[data-theme="${theme.name.toLowerCase()}"][data-theme-mode="${mode}"]`;
      }

      // Generate CSS variables
      const colorVars = createCSSVarMap(theme.colors, "colors");
      const tokenVars = createCSSVarMap(selectedMode, "tokens");
      const designTokenVars = createCSSVarMap(theme.designTokens, "design");

      const allVars = { ...colorVars, ...tokenVars, ...designTokenVars };
      const cssVarDeclarations = Object.entries(allVars)
        .map(([varName, value]) => `  ${varName}: ${value};`)
        .join("\n");

      cssBlocks.push(`${selector} {\n${cssVarDeclarations}\n}`);
    });
  });

  // Add utility CSS
  const utilityCSS = `
/* Theme utility classes */
.theme-transition {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}`;

  const header = `/* Auto-generated theme CSS variables */\n/* Generated at: ${new Date().toISOString()} */`;

  return `${header}\n\n${cssBlocks.join("\n\n")}\n${utilityCSS}`;
};

import type { NuiTheme } from "../types";
import { createCSSVarMap } from "./css";

export interface CSSGenerationOptions {
  /** Whether to minify the generated CSS */
  minify?: boolean;
  /** Whether to include fallback variables */
  includeFallbacks?: boolean;
  /** Custom CSS variable prefix */
  prefix?: string;
  /** Whether to include utility classes */
  includeUtilities?: boolean;
  /** Whether to validate theme structure */
  validate?: boolean;
}

export interface CSSVariableMap {
  [selector: string]: Record<string, string>;
}

/**
 * Validates theme structure for CSS generation
 */
export const validateTheme = (theme: NuiTheme): string[] => {
  const errors: string[] = [];

  if (!theme.name) {
    errors.push("Theme must have a name");
  }

  if (!theme.modes || Object.keys(theme.modes).length === 0) {
    errors.push("Theme must have at least one mode");
  }

  if (!theme.colors) {
    errors.push("Theme must have colors defined");
  }

  return errors;
};

/**
 * Generates CSS content with CSS variables for a given theme and mode.
 */
export const generateThemeCSS = (
  theme: NuiTheme,
  mode: string = "light",
  selector: string = ":root"
): string => {
  const selectedMode =
    theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;

  // Generate CSS variables for colors, tokens, and design tokens
  const colorVars = createCSSVarMap(theme.colors, "colors");
  const tokenVars = createCSSVarMap(selectedMode, "tokens");
  const designTokenVars = createCSSVarMap(theme.designTokens, "design");

  // Combine all variables
  const allVars = {
    ...colorVars,
    ...tokenVars,
    ...designTokenVars,
  };

  // Generate CSS content
  const cssVarDeclarations = Object.entries(allVars)
    .map(([varName, value]) => `  ${varName}: ${value};`)
    .join("\n");

  return `${selector} {\n${cssVarDeclarations}\n}`;
};

/**
 * Minifies CSS by removing unnecessary whitespace
 */
export const minifyCSS = (css: string): string => {
  return css
    .replace(/\/\*[\s\S]*?\*\//g, "") // Remove comments
    .replace(/\s+/g, " ") // Replace multiple spaces with single space
    .replace(/;\s*}/g, "}") // Remove semicolon before closing brace
    .replace(/{\s*/g, "{") // Remove space after opening brace
    .replace(/}\s*/g, "}") // Remove space after closing brace
    .replace(/:\s*/g, ":") // Remove space after colon
    .replace(/;\s*/g, ";") // Remove space after semicolon
    .trim();
};

/**
 * Enhanced CSS generation with options
 */
export const generateThemeCSSWithOptions = (
  theme: NuiTheme,
  mode: string = "light",
  selector: string = ":root",
  options: CSSGenerationOptions = {}
): string => {
  const { validate = true, minify = false, includeFallbacks = true } = options;

  // Validate theme if requested
  if (validate) {
    const errors = validateTheme(theme);
    if (errors.length > 0) {
      throw new Error(`Theme validation failed: ${errors.join(", ")}`);
    }
  }

  const selectedMode =
    theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;

  // Generate CSS variables for colors, tokens, and design tokens
  const colorVars = createCSSVarMap(theme.colors, "colors");
  const tokenVars = createCSSVarMap(selectedMode, "tokens");
  const designTokenVars = createCSSVarMap(theme.designTokens, "design");

  // Combine all variables
  const allVars = {
    ...colorVars,
    ...tokenVars,
    ...designTokenVars,
  };

  // Add fallbacks if requested
  if (includeFallbacks) {
    const fallbacks = {
      "--fallback-color": "#000000",
      "--fallback-background": "#ffffff",
      "--fallback-border": "#e5e5e5",
      "--fallback-text": "#000000",
      "--fallback-surface": "#ffffff",
    };
    Object.assign(allVars, fallbacks);
  }

  // Generate CSS content
  const indent = minify ? "" : "  ";
  const newline = minify ? "" : "\n";
  const space = minify ? "" : " ";

  const cssVarDeclarations = Object.entries(allVars)
    .map(([varName, value]) => `${indent}${varName}:${space}${value};`)
    .join(newline);

  const css = `${selector}${space}{${newline}${cssVarDeclarations}${newline}}`;

  return minify ? minifyCSS(css) : css;
};

/**
 * Generates CSS content for all modes of a theme.
 */
export const generateThemeModesCSS = (theme: NuiTheme): string => {
  const modes = Object.keys(theme.modes);

  return modes
    .map((mode) => {
      const selector =
        mode === "light" ? ":root" : `[data-theme-mode="${mode}"]`;
      return generateThemeCSS(theme, mode, selector);
    })
    .join("\n\n");
};

/**
 * Generates CSS content for multiple themes with all their modes.
 */
export const generateMultiThemeCSS = (themes: NuiTheme[]): string => {
  return themes
    .map((theme, index) => {
      const themeSelector =
        index === 0 ? "" : `[data-theme="${theme.name.toLowerCase()}"]`;
      const modes = Object.keys(theme.modes);

      return modes
        .map((mode) => {
          let selector = ":root";

          if (themeSelector && mode === "light") {
            selector = themeSelector;
          } else if (themeSelector && mode !== "light") {
            selector = `${themeSelector}[data-theme-mode="${mode}"]`;
          } else if (!themeSelector && mode !== "light") {
            selector = `[data-theme-mode="${mode}"]`;
          }

          return generateThemeCSS(theme, mode, selector);
        })
        .join("\n\n");
    })
    .join("\n\n");
};

/**
 * Generates a complete CSS file with theme variables and utility classes.
 */
export const generateCompleteThemeCSS = (themes: NuiTheme[]): string => {
  const themeCSS = generateMultiThemeCSS(themes);

  const utilityCSS = `
/* Theme utility classes */
.theme-transition {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* CSS variable fallbacks */
:root {
  --fallback-color: #000000;
  --fallback-background: #ffffff;
  --fallback-border: #e5e5e5;
}
`;

  return `/* Auto-generated theme CSS variables */\n/* Generated at: ${new Date().toISOString()} */\n\n${themeCSS}\n${utilityCSS}`;
};

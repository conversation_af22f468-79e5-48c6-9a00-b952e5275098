import { readFileSync, existsSync } from "fs";
import { join } from "path";
import type { NuiTheme } from "../types";

export interface TreeShakingOptions {
  /** Source directories to scan for CSS variable usage */
  sourceDirs: string[];
  /** File extensions to include in the scan */
  extensions: string[];
  /** Whether to include commonly used variables even if not found */
  includeCommon?: boolean;
  /** Custom patterns to match CSS variable usage */
  customPatterns?: RegExp[];
}

export interface UsageAnalysis {
  /** CSS variables that are used in the codebase */
  usedVariables: Set<string>;
  /** CSS variables that are defined but not used */
  unusedVariables: Set<string>;
  /** Files where each variable is used */
  usageMap: Map<string, string[]>;
  /** Statistics about usage */
  stats: {
    totalDefined: number;
    totalUsed: number;
    usagePercentage: number;
  };
}

/**
 * Default patterns to match CSS variable usage
 */
const DEFAULT_PATTERNS = [
  /var\(--[\w-]+/g,                    // var(--variable-name)
  /--[\w-]+\s*:/g,                     // CSS variable definitions
  /"--[\w-]+"/g,                       // String literals with CSS variables
  /'--[\w-]+'/g,                       // String literals with CSS variables
  /`--[\w-]+`/g,                       // Template literals with CSS variables
  /style.*--[\w-]+/g,                  // Style attributes with CSS variables
];

/**
 * Commonly used CSS variables that should be included even if not detected
 */
const COMMON_VARIABLES = [
  '--tokens-primary-base',
  '--tokens-text-primary',
  '--tokens-background-primary',
  '--tokens-border-default',
  '--fallback-color',
  '--fallback-background',
];

/**
 * Scans source files for CSS variable usage
 */
export function analyzeVariableUsage(
  themes: NuiTheme[],
  options: TreeShakingOptions
): UsageAnalysis {
  const { sourceDirs, extensions, includeCommon = true, customPatterns = [] } = options;
  
  const usedVariables = new Set<string>();
  const usageMap = new Map<string, string[]>();
  const allPatterns = [...DEFAULT_PATTERNS, ...customPatterns];
  
  // Get all defined variables from themes
  const definedVariables = new Set<string>();
  themes.forEach(theme => {
    Object.keys(theme.modes).forEach(mode => {
      const vars = extractVariablesFromTheme(theme, mode);
      vars.forEach(varName => definedVariables.add(varName));
    });
  });
  
  // Scan source files
  sourceDirs.forEach(sourceDir => {
    if (existsSync(sourceDir)) {
      scanDirectory(sourceDir, extensions, allPatterns, usedVariables, usageMap);
    }
  });
  
  // Add commonly used variables if requested
  if (includeCommon) {
    COMMON_VARIABLES.forEach(varName => {
      if (definedVariables.has(varName)) {
        usedVariables.add(varName);
      }
    });
  }
  
  // Calculate unused variables
  const unusedVariables = new Set<string>();
  definedVariables.forEach(varName => {
    if (!usedVariables.has(varName)) {
      unusedVariables.add(varName);
    }
  });
  
  // Calculate statistics
  const totalDefined = definedVariables.size;
  const totalUsed = usedVariables.size;
  const usagePercentage = totalDefined > 0 ? (totalUsed / totalDefined) * 100 : 0;
  
  return {
    usedVariables,
    unusedVariables,
    usageMap,
    stats: {
      totalDefined,
      totalUsed,
      usagePercentage,
    },
  };
}

/**
 * Extracts CSS variable names from a theme and mode
 */
function extractVariablesFromTheme(theme: NuiTheme, mode: string): string[] {
  const variables: string[] = [];
  
  // This would need to be implemented based on your theme structure
  // For now, return empty array as placeholder
  // In a real implementation, you'd extract all possible variable names
  
  return variables;
}

/**
 * Recursively scans a directory for files and analyzes CSS variable usage
 */
function scanDirectory(
  dir: string,
  extensions: string[],
  patterns: RegExp[],
  usedVariables: Set<string>,
  usageMap: Map<string, string[]>
): void {
  // Implementation would recursively scan files
  // This is a simplified placeholder
}

/**
 * Analyzes a single file for CSS variable usage
 */
function analyzeFile(
  filePath: string,
  patterns: RegExp[],
  usedVariables: Set<string>,
  usageMap: Map<string, string[]>
): void {
  try {
    const content = readFileSync(filePath, 'utf8');
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          // Extract variable name from match
          const varName = extractVariableName(match);
          if (varName) {
            usedVariables.add(varName);
            
            if (!usageMap.has(varName)) {
              usageMap.set(varName, []);
            }
            usageMap.get(varName)!.push(filePath);
          }
        });
      }
    });
  } catch (error) {
    console.warn(`Failed to analyze file ${filePath}:`, error);
  }
}

/**
 * Extracts CSS variable name from a matched string
 */
function extractVariableName(match: string): string | null {
  const varMatch = match.match(/--[\w-]+/);
  return varMatch ? varMatch[0] : null;
}

/**
 * Generates optimized CSS with only used variables
 */
export function generateOptimizedCSS(
  themes: NuiTheme[],
  usedVariables: Set<string>,
  options: any = {}
): string {
  // Implementation would generate CSS with only the used variables
  // This is a placeholder
  return '';
}

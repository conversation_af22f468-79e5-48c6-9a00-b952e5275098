/**
 * Converts a deeply nested object into a flat map of CSS custom properties.
 * Each property is formatted as `--prefix-key-key: value`.
 */
export const createCSSVarMap = (
  source: Record<string, unknown>,
  prefix = ""
): Record<string, string> => {
  const result: Record<string, string> = {};

  // Recursively walks through the object and flattens keys into CSS variable format.
  const flattenToVars = (obj: Record<string, unknown>, path: string[] = []) => {
    for (const [key, value] of Object.entries(obj)) {
      // Build the next path segment
      const currentPath = [...path, key];

      if (typeof value === "string") {
        // Join path segments to form the final CSS variable name
        const pathString = currentPath.join("-");
        const variableName = prefix
          ? `--${prefix}-${pathString}`
          : `--${pathString}`;
        result[variableName] = value;
      } else if (isPlainObject(value)) {
        // Recurse into nested objects
        flattenToVars(value, currentPath);
      }
    }
  };

  flattenToVars(source);
  return result;
};

/**
 * Utility: Determines whether a value is a plain object.
 */
const isPlainObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === "object" && value !== null && !Array.isArray(value);
};

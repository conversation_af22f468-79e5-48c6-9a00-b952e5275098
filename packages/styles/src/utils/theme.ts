import { deepmerge } from "deepmerge-ts";

import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme, NuiThemeOptions, NuiThemeVars } from "../types";
import { createCSSVarMap } from "./css";

/**
 * The base theme used as the foundation for all custom themes.
 */
const baseTheme: NuiTheme = {
  name: "Base",
  colors,
  modes: { ...colorModes },
  designTokens,
  variantTokens,
  components: {},
};

/**
 * Creates a new theme by merging user options with the base theme,
 * and generates CSS variable mappings for it.
 */
export const createTheme = (
  options: NuiThemeOptions | ((base: NuiTheme) => NuiThemeOptions) = {
    name: "Custom",
  }
): NuiTheme => {
  const resolved = typeof options === "function" ? options(baseTheme) : options;
  const merged = deepmerge(baseTheme, resolved);
  return { ...merged, vars: mapThemeToCSSVars(merged) };
};

/**
 * Converts a theme's color palette and mode-specific tokens into CSS variable maps.
 */
export const mapThemeToCSSVars = (
  theme: NuiTheme,
  mode = "light"
): NuiThemeVars => {
  const selectedMode =
    theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;

  return {
    colors: createCSSVarMap(theme.colors, "colors"),
    tokens: createCSSVarMap(selectedMode, "tokens"),
  };
};

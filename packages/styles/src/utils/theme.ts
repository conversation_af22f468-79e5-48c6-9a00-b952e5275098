import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme } from "../types";

/**
 * Theme creation context providing access to default theme building blocks
 */
export interface ThemeContext {
  /** Available color modes (light, dark, etc.) */
  modes: typeof colorModes;
  /** Default color palette */
  colors: typeof colors;
  /** Default design tokens */
  designTokens: typeof designTokens;
  /** Default variant tokens */
  variantTokens: typeof variantTokens;
}

/**
 * Theme configuration function type
 */
export type ThemeConfig = (context: ThemeContext) => Partial<NuiTheme> & {
  name: string;
};

/**
 * Creates a theme with access to default building blocks and proper defaults
 */
export const createTheme = (config: ThemeConfig): NuiTheme => {
  const context: ThemeContext = {
    modes: colorModes,
    colors,
    designTokens,
    variantTokens,
  };

  const themeConfig = config(context);

  // Ensure required properties and provide sensible defaults
  return {
    name: themeConfig.name,
    colors: themeConfig.colors || colors,
    modes: themeConfig.modes || { light: colorModes.light, dark: colorModes.dark },
    designTokens: themeConfig.designTokens || designTokens,
    variantTokens: themeConfig.variantTokens || variantTokens,
    components: themeConfig.components || {},
  };
};

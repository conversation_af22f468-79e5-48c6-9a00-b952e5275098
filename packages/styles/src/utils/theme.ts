import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme } from "../types";

/**
 * Base theme with all default values
 */
const baseTheme: NuiTheme = {
  name: "Base",
  colors,
  modes: {
    light: colorModes.light,
    dark: colorModes.dark,
  },
  designTokens,
  variantTokens,
  components: {},
};

/**
 * Deep merge utility for nested objects
 */
const deepMerge = (target: any, source: any): any => {
  if (!source) return target;

  const result = { ...target };

  for (const key in source) {
    if (
      source[key] &&
      typeof source[key] === "object" &&
      !Array.isArray(source[key])
    ) {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }

  return result;
};

/**
 * Creates a theme by extending the base theme with custom overrides
 */
export const createTheme = (
  overrides: Partial<NuiTheme> & { name: string }
): NuiTheme => {
  // Deep merge the overrides with the base theme
  return deepMerge(baseTheme, overrides);
};

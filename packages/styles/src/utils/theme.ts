import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme } from "../types";

/**
 * Base theme with all default values
 */
const baseTheme: NuiTheme = {
  name: "Base",
  colors,
  modes: {
    light: colorModes.light,
    dark: colorModes.dark,
  },
  designTokens,
  variantTokens,
  components: {},
};

/**
 * Theme creation context providing access to base theme and building blocks
 */
export interface ThemeContext {
  /** Base theme with all defaults */
  baseTheme: NuiTheme;
  /** Available color modes (light, dark, etc.) */
  modes: typeof colorModes;
  /** Default color palette */
  colors: typeof colors;
  /** Default design tokens */
  designTokens: typeof designTokens;
  /** Default variant tokens */
  variantTokens: typeof variantTokens;
}

/**
 * Theme configuration function type - only requires name, everything else is optional
 */
export type ThemeConfig = (context: ThemeContext) => Partial<NuiTheme> & {
  name: string;
};

/**
 * Creates a theme by extending the base theme with custom overrides
 */
export const createTheme = (config: ThemeConfig): NuiTheme => {
  const context: ThemeContext = {
    baseTheme,
    modes: colorModes,
    colors,
    designTokens,
    variantTokens,
  };

  const themeConfig = config(context);

  // Merge with base theme, allowing overrides
  return {
    ...baseTheme,
    ...themeConfig,
    // Deep merge modes if provided
    modes: themeConfig.modes
      ? { ...baseTheme.modes, ...themeConfig.modes }
      : baseTheme.modes,
    // Deep merge colors if provided
    colors: themeConfig.colors
      ? { ...baseTheme.colors, ...themeConfig.colors }
      : baseTheme.colors,
    // Deep merge design tokens if provided
    designTokens: themeConfig.designTokens
      ? { ...baseTheme.designTokens, ...themeConfig.designTokens }
      : baseTheme.designTokens,
    // Deep merge variant tokens if provided
    variantTokens: themeConfig.variantTokens
      ? { ...baseTheme.variantTokens, ...themeConfig.variantTokens }
      : baseTheme.variantTokens,
    // Deep merge components if provided
    components: themeConfig.components
      ? { ...baseTheme.components, ...themeConfig.components }
      : baseTheme.components,
  };
};

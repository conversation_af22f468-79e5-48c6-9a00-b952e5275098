import type { Plugin } from "vite";
import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join } from "path";
import type { NuiTheme } from "./types";
import { generateCompleteThemeCSS } from "./utils/build-css";

export interface NextUIVitePluginOptions {
  /** Array of themes to generate CSS for */
  themes: NuiTheme[];
  /** Output directory for generated CSS (relative to project root) */
  outputDir?: string;
  /** Whether to generate individual theme files */
  generateIndividualFiles?: boolean;
  /** Whether to inject CSS into the build */
  injectCSS?: boolean;
}

/**
 * Vite plugin for generating Next UI theme CSS variables at build time
 */
export function nextUIVitePlugin(options: NextUIVitePluginOptions): Plugin {
  const {
    themes,
    outputDir = "dist/css",
    generateIndividualFiles = true,
    injectCSS = true,
  } = options;

  let generatedCSS = "";

  return {
    name: "next-ui-theme-generator",
    buildStart() {
      // Generate CSS during build start
      console.log("🎨 Generating Next UI theme CSS variables...");
      
      try {
        // Generate combined CSS
        generatedCSS = generateCompleteThemeCSS(themes);
        
        // Write to file system if outputDir is specified
        if (outputDir) {
          const fullOutputDir = join(process.cwd(), outputDir);
          
          if (!existsSync(fullOutputDir)) {
            mkdirSync(fullOutputDir, { recursive: true });
          }
          
          // Write combined CSS file
          const combinedFilePath = join(fullOutputDir, "themes.css");
          writeFileSync(combinedFilePath, generatedCSS, "utf8");
          console.log(`✓ Generated ${combinedFilePath}`);
          
          // Generate individual theme files if requested
          if (generateIndividualFiles) {
            themes.forEach((theme) => {
              const modes = Object.keys(theme.modes);
              
              modes.forEach((mode) => {
                const themeCSS = generateThemeCSS(theme, mode, ":root");
                const filename = `${theme.name.toLowerCase()}-${mode}.css`;
                const filepath = join(fullOutputDir, filename);
                
                writeFileSync(filepath, themeCSS, "utf8");
                console.log(`✓ Generated ${filename}`);
              });
            });
          }
        }
        
        console.log("✅ Next UI theme CSS generation completed!");
      } catch (error) {
        console.error("❌ Error generating Next UI theme CSS:", error);
        throw error;
      }
    },
    
    generateBundle() {
      // Inject CSS into the bundle if requested
      if (injectCSS && generatedCSS) {
        this.emitFile({
          type: "asset",
          fileName: "next-ui-themes.css",
          source: generatedCSS,
        });
      }
    },
    
    load(id) {
      // Handle virtual imports for theme CSS
      if (id === "virtual:next-ui-themes") {
        return `export default ${JSON.stringify(generatedCSS)};`;
      }
      
      if (id === "virtual:next-ui-themes.css") {
        return generatedCSS;
      }
    },
    
    resolveId(id) {
      if (id === "virtual:next-ui-themes" || id === "virtual:next-ui-themes.css") {
        return id;
      }
    },
  };
}

// Helper function to generate individual theme CSS (used by the plugin)
function generateThemeCSS(theme: NuiTheme, mode: string, selector: string): string {
  const selectedMode = theme.modes[mode as keyof typeof theme.modes] || theme.modes.light;
  
  // This is a simplified version - you might want to import from build-css.ts
  const colorVars = Object.entries(theme.colors).map(([key, value]) => 
    `  --colors-${key}: ${value};`
  ).join('\n');
  
  const tokenVars = Object.entries(selectedMode).map(([key, value]) => 
    `  --tokens-${key}: ${value};`
  ).join('\n');
  
  return `${selector} {\n${colorVars}\n${tokenVars}\n}`;
}

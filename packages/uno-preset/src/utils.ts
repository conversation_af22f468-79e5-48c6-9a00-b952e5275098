import type { NuiTheme } from "@next-ui/styles";

/**
 * Flattens nested objects into dot notation keys
 */
const flattenObject = (obj: any, prefix = ""): Record<string, string> => {
  const result: Record<string, string> = {};

  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}-${key}` : key;

    if (typeof value === "string") {
      result[newKey] = `var(--tokens-${newKey.replace(/\./g, "-")})`;
    } else if (
      typeof value === "object" &&
      value !== null &&
      !Array.isArray(value)
    ) {
      Object.assign(result, flattenObject(value, newKey));
    }
  }

  return result;
};

/**
 * Maps a `NuiTheme` to an UnoCSS-compatible theme format with token utilities.
 */
export const mapThemeToUnoCSS = (theme: NuiTheme) => {
  // Get the first mode (usually light) to extract token structure
  const firstMode = Object.values(theme.modes)[0];

  // Flatten tokens into utility-friendly format
  const tokenColors = flattenObject(firstMode);

  return {
    colors: {
      // Include base colors
      ...theme.colors,
      // Include token-based colors for utilities
      ...tokenColors,
    },
    backgroundColor: tokenColors,
    textColor: tokenColors,
    borderColor: tokenColors,
  };
};
